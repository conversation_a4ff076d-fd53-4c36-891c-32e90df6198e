package buyerregistrationrequest

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	service "content-service/internal/service/buyer_registration_request"
	"net/http"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service   service.BuyerRegistrationRequestService
	ErpConfig global.ErpConfig
}

func (h *Handler) SearchBuyerRegistrationRequestFilter(c *fiber.Ctx) error {
	var req dto.BuyerRegistrationRequestPageReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.SearchBuyerRegistrationRequestFilter(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}
