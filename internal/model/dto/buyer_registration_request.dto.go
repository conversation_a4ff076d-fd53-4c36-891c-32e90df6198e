package dto

import (
	"backend-common-lib/model"
	"content-service/internal/model/entity"
	"time"
)

type BuyerRegistrationRequestPageReqDto struct {
	model.PagingRequest
	MemberDetails  *string                   `json:"memberDetails"`
	AccountType    *string                   `json:"accountType"`
	Nationality    *string                   `json:"nationality"`
	ApprovalStatus entity.ApprovalStatusEnum `json:"approvalStatus"`
	Search         *string                   `json:"search"`
}

type BuyerRegistrationRequestDto struct {
	model.BaseDto
	RequestDate             *time.Time `json:"requestDate"`  // วันที่ส่งคำขอ
	PersonTypeId            *string    `json:"personTypeId"` // ประเภทบุคคล เช่น บุคคลธรรมดา / นิติบุคคล
	PersonTypeTh            *string    `json:"personTypeTh"` // ประเภทบุคคล เช่น บุคคลธรรมดา / นิติบุคคล
	PersonTypeEn            *string    `json:"personTypeEn"` // ประเภทบุคคล เช่น บุคคลธรรมดา / นิติบุคคล
	NationalId              *string    `json:"nationalId"`   // เลขประจำตัวประชาชน / เลขนิติบุคคล
	TitleTh                 *string    `json:"titleTh"`      // คำนำหน้า เช่น นาย / นาง / นางสาว / Mr / Ms
	<PERSON>En                 *string    `json:"titleEn"`      // คำนำหน้า เช่น นาย / นาง / นางสาว / Mr / Ms
	FirstNameTh             *string    `json:"firstNameTh"`  // ชื่อ
	FirstNameEn             *string    `json:"firstNameEn"`  // ชื่อ
	LastNameTh              *string    `json:"lastNameTh"`   // นามสกุล
	LastNameEn              *string    `json:"lastNameEn"`   // นามสกุล
	DateOfBirth             *time.Time `json:"dateOfBirth"`  // วันเดือนปีเกิด
	RegisteredAddressTypeId *int       `json:"registeredAddressTypeId"`
	RegisteredAddress       *string    `json:"registeredAddress"` // ที่อยู่ตามทะเบียนบ้าน
	DocumentAddressTypeId   *int       `json:"documentAddressTypeId"`
	DocumentAddress         *string    `json:"documentAddress"` // ที่อยู่ส่งเอกสาร
	RegistrationBookTypeId  *int       `json:"registrationBookTypeId"`
	RegistrationBookAddress *string    `json:"registrationBookAddress"` // ที่อยู่ส่งเล่มทะเบียน
	ShippingAddressTypeId   *int       `json:"shippingAddressTypeId"`
	ShippingAddress         *string    `json:"shippingAddress"` // ที่อยู่ส่งสินค้า
	ApprovalStatus          *string    `json:"approvalStatus"`  // รหัสสถานะ เช่น WAITING, APPROVED, REJECTED
	RejectReason            *string    `json:"rejectReason"`    // เหตุผลการปฏิเสธ (ถ้ามี)

}

// New DTO for the complex SQL query response
type BuyerRegistrationRequestDetailDto struct {
	// Basic buyer registration request fields
	ID             int                       `json:"id"`
	BuyerID        int                       `json:"buyerId"`
	RequestDate    *time.Time                `json:"requestDate"`
	ApprovalStatus entity.ApprovalStatusEnum `json:"approvalStatus"`
	Remark         *string                   `json:"remark"`

	// File information
	IdCardFile      *string `json:"idCardFile"`
	PermitDocFile   *string `json:"permitDocFile"`
	BankAccountFile *string `json:"bankAccountFile"`

	// Customer type information
	CustomerTypeTh   *string `json:"customerTypeTh"`
	CustomerTypeEn   *string `json:"customerTypeEn"`
	CustomerTypeCode *string `json:"customerTypeCode"`

	// Buyer basic information
	PrefixNameID         int        `json:"prefixNameId"`
	PrefixTh             *string    `json:"prefixTh"`
	PrefixEn             *string    `json:"prefixEn"`
	FirstName            *string    `json:"firstName"`
	MiddleName           *string    `json:"middleName"`
	LastName             *string    `json:"lastName"`
	DateOfBirth          *time.Time `json:"dateOfBirth"`
	Nationality          *string    `json:"nationality"`
	IdentificationNumber *string    `json:"identificationNumber"`

	// Registered Address (TYPE 1)
	RegAddressTypeID                 int     `json:"regAddressTypeId"`
	RegHouseNumber                   *string `json:"regHouseNumber"`
	RegMoo                           *string `json:"regMoo"`
	RegVillage                       *string `json:"regVillage"`
	RegRoomNumber                    *string `json:"regRoomNumber"`
	RegFloor                         *string `json:"regFloor"`
	RegBuilding                      *string `json:"regBuilding"`
	RegSoi                           *string `json:"regSoi"`
	RegRoad                          *string `json:"regRoad"`
	RegPostCodeID                    *int    `json:"regPostCodeId"`
	RegPostCodeTh                    *string `json:"regPostCodeTh"`
	RegPostCodeEn                    *string `json:"regPostCodeEn"`
	RegMasterSubDistrictCode         *string `json:"regMasterSubDistrictCode"`
	RegMasterPostCodeSubDistrictCode *string `json:"regMasterPostCodeSubDistrictCode"`
	RegMasterCityDescriptionTh       *string `json:"regMasterCityDescriptionTh"`
	RegMasterCityDescriptionEn       *string `json:"regMasterCityDescriptionEn"`

	// Document Address (TYPE 2)
	DocAddressTypeID                 int     `json:"docAddressTypeId"`
	DocHouseNumber                   *string `json:"docHouseNumber"`
	DocMoo                           *string `json:"docMoo"`
	DocVillage                       *string `json:"docVillage"`
	DocRoomNumber                    *string `json:"docRoomNumber"`
	DocFloor                         *string `json:"docFloor"`
	DocBuilding                      *string `json:"docBuilding"`
	DocSoi                           *string `json:"docSoi"`
	DocRoad                          *string `json:"docRoad"`
	DocPostCodeID                    *int    `json:"docPostCodeId"`
	DocPostCodeTh                    *string `json:"docPostCodeTh"`
	DocPostCodeEn                    *string `json:"docPostCodeEn"`
	DocMasterSubDistrictCode         *string `json:"docMasterSubDistrictCode"`
	DocMasterPostCodeSubDistrictCode *string `json:"docMasterPostCodeSubDistrictCode"`
	DocMasterCityDescriptionTh       *string `json:"docMasterCityDescriptionTh"`
	DocMasterCityDescriptionEn       *string `json:"docMasterCityDescriptionEn"`

	// Registration Book Address (TYPE 3)
	BookAddressTypeID                 int     `json:"bookAddressTypeId"`
	BookHouseNumber                   *string `json:"bookHouseNumber"`
	BookMoo                           *string `json:"bookMoo"`
	BookVillage                       *string `json:"bookVillage"`
	BookRoomNumber                    *string `json:"bookRoomNumber"`
	BookFloor                         *string `json:"bookFloor"`
	BookBuilding                      *string `json:"bookBuilding"`
	BookSoi                           *string `json:"bookSoi"`
	BookRoad                          *string `json:"bookRoad"`
	BookPostCodeID                    *int    `json:"bookPostCodeId"`
	BookPostCodeTh                    *string `json:"bookPostCodeTh"`
	BookPostCodeEn                    *string `json:"bookPostCodeEn"`
	BookMasterSubDistrictCode         *string `json:"bookMasterSubDistrictCode"`
	BookMasterPostCodeSubDistrictCode *string `json:"bookMasterPostCodeSubDistrictCode"`
	BookMasterCityDescriptionTh       *string `json:"bookMasterCityDescriptionTh"`
	BookMasterCityDescriptionEn       *string `json:"bookMasterCityDescriptionEn"`

	// Shipping Address (TYPE 4)
	ShipAddressTypeID                 int     `json:"shipAddressTypeId"`
	ShipHouseNumber                   *string `json:"shipHouseNumber"`
	ShipMoo                           *string `json:"shipMoo"`
	ShipVillage                       *string `json:"shipVillage"`
	ShipRoomNumber                    *string `json:"shipRoomNumber"`
	ShipFloor                         *string `json:"shipFloor"`
	ShipBuilding                      *string `json:"shipBuilding"`
	ShipSoi                           *string `json:"shipSoi"`
	ShipRoad                          *string `json:"shipRoad"`
	ShipPostCodeID                    *int    `json:"shipPostCodeId"`
	ShipPostCodeTh                    *string `json:"shipPostCodeTh"`
	ShipPostCodeEn                    *string `json:"shipPostCodeEn"`
	ShipMasterSubDistrictCode         *string `json:"shipMasterSubDistrictCode"`
	ShipMasterPostCodeSubDistrictCode *string `json:"shipMasterPostCodeSubDistrictCode"`
	ShipMasterCityDescriptionTh       *string `json:"shipMasterCityDescriptionTh"`
	ShipMasterCityDescriptionEn       *string `json:"shipMasterCityDescriptionEn"`
}

type BuyerRegistrationRequestPageRespDto[T any] struct {
	model.PagingModel[T]
}

type BuyerRegistrationRequestUpdateReqDto struct {
	model.BaseDtoActionBy
	ApprovalStatus entity.ApprovalStatusEnum `json:"approvalStatus"`
}
