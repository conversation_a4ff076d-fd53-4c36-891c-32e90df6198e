package dto

import (
	"backend-common-lib/model"
	"content-service/internal/model/entity"
	"time"
)

type BuyerRegistrationRequestPageReqDto struct {
	model.PagingRequest
	MemberDetails  *string                   `json:"memberDetails"`
	AccountType    *string                   `json:"accountType"`
	Nationality    *string                   `json:"nationality"`
	ApprovalStatus entity.ApprovalStatusEnum `json:"approvalStatus"`
	Search         *string                   `json:"search"`
}

type BuyerRegistrationRequestDto struct {
	model.BaseDto
	RequestDate       *time.Time `json:"requestDate"`       // วันที่ส่งคำขอ
	IdIdCardFile      *string    `json:"idIdCardFile"`      // id ไฟล์เลขที่บัตรประชาชน
	IdCardFile        *string    `json:"idCardFile"`        // ไฟล์เลขที่บัตรประชาชน
	IdPermitDocFile   *string    `json:"idPermitDocFile"`   // id ไฟล์เอกสารอนุญาติ
	PermitDocFile     *string    `json:"permitDocFile"`     // ไฟล์เอกสารอนุญาติ
	IdBankAccountFile *string    `json:"idBankAccountFile"` // id ไฟล์เอกสารบัญชีธนาคาร
	BankAccountFile   *string    `json:"bankAccountFile"`   // ไฟล์เอกสารบัญชีธนาคาร

	PersonTypeId            *string    `json:"personTypeId"` // ประเภทบุคคล เช่น บุคคลธรรมดา / นิติบุคคล
	PersonTypeTh            *string    `json:"personTypeTh"` // ประเภทบุคคล เช่น บุคคลธรรมดา / นิติบุคคล
	PersonTypeEn            *string    `json:"personTypeEn"` // ประเภทบุคคล เช่น บุคคลธรรมดา / นิติบุคคล
	NationalId              *string    `json:"nationalId"`   // เลขประจำตัวประชาชน / เลขนิติบุคคล
	TitleTh                 *string    `json:"titleTh"`      // คำนำหน้า เช่น นาย / นาง / นางสาว / Mr / Ms
	TitleEn                 *string    `json:"titleEn"`      // คำนำหน้า เช่น นาย / นาง / นางสาว / Mr / Ms
	FirstNameTh             *string    `json:"firstNameTh"`  // ชื่อ
	FirstNameEn             *string    `json:"firstNameEn"`  // ชื่อ
	LastNameTh              *string    `json:"lastNameTh"`   // นามสกุล
	LastNameEn              *string    `json:"lastNameEn"`   // นามสกุล
	DateOfBirth             *time.Time `json:"dateOfBirth"`  // วันเดือนปีเกิด
	RegisteredAddressTypeId *int       `json:"registeredAddressTypeId"`
	RegisteredAddress       *string    `json:"registeredAddress"` // ที่อยู่ตามทะเบียนบ้าน
	DocumentAddressTypeId   *int       `json:"documentAddressTypeId"`
	DocumentAddress         *string    `json:"documentAddress"` // ที่อยู่ส่งเอกสาร
	RegistrationBookTypeId  *int       `json:"registrationBookTypeId"`
	RegistrationBookAddress *string    `json:"registrationBookAddress"` // ที่อยู่ส่งเล่มทะเบียน
	ShippingAddressTypeId   *int       `json:"shippingAddressTypeId"`
	ShippingAddress         *string    `json:"shippingAddress"` // ที่อยู่ส่งสินค้า
	ApprovalStatus          *string    `json:"approvalStatus"`  // รหัสสถานะ เช่น WAITING, APPROVED, REJECTED
	RejectReason            *string    `json:"rejectReason"`    // เหตุผลการปฏิเสธ (ถ้ามี)

}

type BuyerRegistrationRequestPageRespDto[T any] struct {
	model.PagingModel[T]
}

type BuyerRegistrationRequestUpdateReqDto struct {
	model.BaseDtoActionBy
	ApprovalStatus entity.ApprovalStatusEnum `json:"approvalStatus"`
	Remark         *string                   `json:"remark"`
}
