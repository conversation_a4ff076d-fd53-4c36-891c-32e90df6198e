package entity

import (
	"backend-common-lib/model"
	"time"
)

type ApprovalStatusEnum string

const (
	ApprovalStatusPending  ApprovalStatusEnum = "WAITING"
	ApprovalStatusApproved ApprovalStatusEnum = "APPROVED"
	ApprovalStatusRejected ApprovalStatusEnum = "REJECTED"
)

type BuyerRegistrationRequest struct {
	*model.BaseEntity
	BuyerID        int                `gorm:"column:buyer_id;not null" json:"buyer_id"`
	IsActive       bool               `gorm:"column:is_active;not null" json:"is_active"`
	IsDeleted      bool               `gorm:"column:is_deleted;not null" json:"is_deleted"`
	RequestDate    *time.Time         `gorm:"column:request_date" json:"request_date"`
	ApprovalStatus ApprovalStatusEnum `gorm:"column:approval_status;type:approval_status_enum;default:PENDING;not null" json:"approval_status"`
}

func (BuyerRegistrationRequest) TableName() string {
	return "buyer_registration_request"
}
