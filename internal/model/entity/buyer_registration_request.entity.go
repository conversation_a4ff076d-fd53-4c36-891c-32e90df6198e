package entity

import (
	"backend-common-lib/model"
	"time"
)

type ApprovalStatusEnum string

const (
	ApprovalStatusPending  ApprovalStatusEnum = "WAITING"
	ApprovalStatusApproved ApprovalStatusEnum = "APPROVED"
	ApprovalStatusRejected ApprovalStatusEnum = "REJECTED"
)

type BuyerRegistrationRequest struct {
	*model.BaseEntity
	BuyerID        int                `gorm:"column:buyer_id;not null" json:"buyer_id"`
	IsActive       bool               `gorm:"column:is_active;not null" json:"is_active"`
	IsDeleted      bool               `gorm:"column:is_deleted;not null" json:"is_deleted"`
	RequestDate    *time.Time         `gorm:"column:request_date" json:"request_date"`
	ApprovalStatus ApprovalStatusEnum `gorm:"column:approval_status;type:approval_status_enum;default:PENDING;not null" json:"approval_status"`
}

func (BuyerRegistrationRequest) TableName() string {
	return "buyer_registration_request"
}

// BuyerRegistrationRequestDetail represents the complex query response for buyer registration request details
type BuyerRegistrationRequestDetail struct {
	// Basic buyer registration request fields
	ID             int                `json:"id"`
	BuyerID        int                `json:"buyerId"`
	RequestDate    *time.Time         `json:"requestDate"`
	ApprovalStatus ApprovalStatusEnum `json:"approvalStatus"`
	Remark         *string            `json:"remark"`

	// File information
	IdCardFile      *string `json:"idCardFile"`
	PermitDocFile   *string `json:"permitDocFile"`
	BankAccountFile *string `json:"bankAccountFile"`

	// Customer type information
	CustomerTypeTh   *string `json:"customerTypeTh"`
	CustomerTypeEn   *string `json:"customerTypeEn"`
	CustomerTypeCode *string `json:"customerTypeCode"`

	// Buyer basic information
	PrefixNameID         int        `json:"prefixNameId"`
	PrefixTh             *string    `json:"prefixTh"`
	PrefixEn             *string    `json:"prefixEn"`
	FirstName            *string    `json:"firstName"`
	MiddleName           *string    `json:"middleName"`
	LastName             *string    `json:"lastName"`
	DateOfBirth          *time.Time `json:"dateOfBirth"`
	Nationality          *string    `json:"nationality"`
	IdentificationNumber *string    `json:"identificationNumber"`

	// Registered Address (TYPE 1)
	RegAddressTypeID                 int     `json:"regAddressTypeId"`
	RegHouseNumber                   *string `json:"regHouseNumber"`
	RegMoo                           *string `json:"regMoo"`
	RegVillage                       *string `json:"regVillage"`
	RegRoomNumber                    *string `json:"regRoomNumber"`
	RegFloor                         *string `json:"regFloor"`
	RegBuilding                      *string `json:"regBuilding"`
	RegSoi                           *string `json:"regSoi"`
	RegRoad                          *string `json:"regRoad"`
	RegPostCodeID                    *int    `json:"regPostCodeId"`
	RegPostCodeTh                    *string `json:"regPostCodeTh"`
	RegPostCodeEn                    *string `json:"regPostCodeEn"`
	RegMasterSubDistrictCode         *string `json:"regMasterSubDistrictCode"`
	RegMasterPostCodeSubDistrictCode *string `json:"regMasterPostCodeSubDistrictCode"`
	RegMasterCityDescriptionTh       *string `json:"regMasterCityDescriptionTh"`
	RegMasterCityDescriptionEn       *string `json:"regMasterCityDescriptionEn"`

	// Document Address (TYPE 2)
	DocAddressTypeID                 int     `json:"docAddressTypeId"`
	DocHouseNumber                   *string `json:"docHouseNumber"`
	DocMoo                           *string `json:"docMoo"`
	DocVillage                       *string `json:"docVillage"`
	DocRoomNumber                    *string `json:"docRoomNumber"`
	DocFloor                         *string `json:"docFloor"`
	DocBuilding                      *string `json:"docBuilding"`
	DocSoi                           *string `json:"docSoi"`
	DocRoad                          *string `json:"docRoad"`
	DocPostCodeID                    *int    `json:"docPostCodeId"`
	DocPostCodeTh                    *string `json:"docPostCodeTh"`
	DocPostCodeEn                    *string `json:"docPostCodeEn"`
	DocMasterSubDistrictCode         *string `json:"docMasterSubDistrictCode"`
	DocMasterPostCodeSubDistrictCode *string `json:"docMasterPostCodeSubDistrictCode"`
	DocMasterCityDescriptionTh       *string `json:"docMasterCityDescriptionTh"`
	DocMasterCityDescriptionEn       *string `json:"docMasterCityDescriptionEn"`

	// Registration Book Address (TYPE 3)
	BookAddressTypeID                 int     `json:"bookAddressTypeId"`
	BookHouseNumber                   *string `json:"bookHouseNumber"`
	BookMoo                           *string `json:"bookMoo"`
	BookVillage                       *string `json:"bookVillage"`
	BookRoomNumber                    *string `json:"bookRoomNumber"`
	BookFloor                         *string `json:"bookFloor"`
	BookBuilding                      *string `json:"bookBuilding"`
	BookSoi                           *string `json:"bookSoi"`
	BookRoad                          *string `json:"bookRoad"`
	BookPostCodeID                    *int    `json:"bookPostCodeId"`
	BookPostCodeTh                    *string `json:"bookPostCodeTh"`
	BookPostCodeEn                    *string `json:"bookPostCodeEn"`
	BookMasterSubDistrictCode         *string `json:"bookMasterSubDistrictCode"`
	BookMasterPostCodeSubDistrictCode *string `json:"bookMasterPostCodeSubDistrictCode"`
	BookMasterCityDescriptionTh       *string `json:"bookMasterCityDescriptionTh"`
	BookMasterCityDescriptionEn       *string `json:"bookMasterCityDescriptionEn"`

	// Shipping Address (TYPE 4)
	ShipAddressTypeID                 int     `json:"shipAddressTypeId"`
	ShipHouseNumber                   *string `json:"shipHouseNumber"`
	ShipMoo                           *string `json:"shipMoo"`
	ShipVillage                       *string `json:"shipVillage"`
	ShipRoomNumber                    *string `json:"shipRoomNumber"`
	ShipFloor                         *string `json:"shipFloor"`
	ShipBuilding                      *string `json:"shipBuilding"`
	ShipSoi                           *string `json:"shipSoi"`
	ShipRoad                          *string `json:"shipRoad"`
	ShipPostCodeID                    *int    `json:"shipPostCodeId"`
	ShipPostCodeTh                    *string `json:"shipPostCodeTh"`
	ShipPostCodeEn                    *string `json:"shipPostCodeEn"`
	ShipMasterSubDistrictCode         *string `json:"shipMasterSubDistrictCode"`
	ShipMasterPostCodeSubDistrictCode *string `json:"shipMasterPostCodeSubDistrictCode"`
	ShipMasterCityDescriptionTh       *string `json:"shipMasterCityDescriptionTh"`
	ShipMasterCityDescriptionEn       *string `json:"shipMasterCityDescriptionEn"`
}
