package entity

import (
	"backend-common-lib/model"
	"time"
)

type ApprovalStatusEnum string

const (
	ApprovalStatusPending  ApprovalStatusEnum = "WAITING"
	ApprovalStatusApproved ApprovalStatusEnum = "APPROVED"
	ApprovalStatusRejected ApprovalStatusEnum = "REJECTED"
)

type BuyerRegistrationRequest struct {
	*model.BaseEntity
	BuyerID        int                `gorm:"column:buyer_id;not null" json:"buyer_id"`
	IsActive       bool               `gorm:"column:is_active;not null" json:"is_active"`
	IsDeleted      bool               `gorm:"column:is_deleted;not null" json:"is_deleted"`
	RequestDate    *time.Time         `gorm:"column:request_date" json:"request_date"`
	ApprovalStatus ApprovalStatusEnum `gorm:"column:approval_status;type:approval_status_enum;default:PENDING;not null" json:"approval_status"`
	Remark         *string            `gorm:"column:remark" json:"remark"`

	// File information - ignored during insert/update to main table
	IdCardFile      *string `gorm:"-" json:"idCardFile"`
	PermitDocFile   *string `gorm:"-" json:"permitDocFile"`
	BankAccountFile *string `gorm:"-" json:"bankAccountFile"`

	// Customer type information - ignored during insert/update to main table
	CustomerTypeTh   *string `gorm:"-" json:"customerTypeTh"`
	CustomerTypeEn   *string `gorm:"-" json:"customerTypeEn"`
	CustomerTypeCode *string `gorm:"-" json:"customerTypeCode"`

	// Buyer basic information - ignored during insert/update to main table
	PrefixNameID         int        `gorm:"-" json:"prefixNameId"`
	PrefixTh             *string    `gorm:"-" json:"prefixTh"`
	PrefixEn             *string    `gorm:"-" json:"prefixEn"`
	FirstName            *string    `gorm:"-" json:"firstName"`
	MiddleName           *string    `gorm:"-" json:"middleName"`
	LastName             *string    `gorm:"-" json:"lastName"`
	DateOfBirth          *time.Time `gorm:"-" json:"dateOfBirth"`
	Nationality          *string    `gorm:"-" json:"nationality"`
	IdentificationNumber *string    `gorm:"-" json:"identificationNumber"`

	// Registered Address (TYPE 1) - ignored during insert/update to main table
	RegAddressTypeID                 int     `gorm:"-" json:"regAddressTypeId"`
	RegHouseNumber                   *string `gorm:"-" json:"regHouseNumber"`
	RegMoo                           *string `gorm:"-" json:"regMoo"`
	RegVillage                       *string `gorm:"-" json:"regVillage"`
	RegRoomNumber                    *string `gorm:"-" json:"regRoomNumber"`
	RegFloor                         *string `gorm:"-" json:"regFloor"`
	RegBuilding                      *string `gorm:"-" json:"regBuilding"`
	RegSoi                           *string `gorm:"-" json:"regSoi"`
	RegRoad                          *string `gorm:"-" json:"regRoad"`
	RegPostCodeID                    *int    `gorm:"-" json:"regPostCodeId"`
	RegPostCodeTh                    *string `gorm:"-" json:"regPostCodeTh"`
	RegPostCodeEn                    *string `gorm:"-" json:"regPostCodeEn"`
	RegMasterSubDistrictCode         *string `gorm:"-" json:"regMasterSubDistrictCode"`
	RegMasterPostCodeSubDistrictCode *string `gorm:"-" json:"regMasterPostCodeSubDistrictCode"`
	RegMasterCityDescriptionTh       *string `gorm:"-" json:"regMasterCityDescriptionTh"`
	RegMasterCityDescriptionEn       *string `gorm:"-" json:"regMasterCityDescriptionEn"`

	// Document Address (TYPE 2) - ignored during insert/update to main table
	DocAddressTypeID                 int     `gorm:"-" json:"docAddressTypeId"`
	DocHouseNumber                   *string `gorm:"-" json:"docHouseNumber"`
	DocMoo                           *string `gorm:"-" json:"docMoo"`
	DocVillage                       *string `gorm:"-" json:"docVillage"`
	DocRoomNumber                    *string `gorm:"-" json:"docRoomNumber"`
	DocFloor                         *string `gorm:"-" json:"docFloor"`
	DocBuilding                      *string `gorm:"-" json:"docBuilding"`
	DocSoi                           *string `gorm:"-" json:"docSoi"`
	DocRoad                          *string `gorm:"-" json:"docRoad"`
	DocPostCodeID                    *int    `gorm:"-" json:"docPostCodeId"`
	DocPostCodeTh                    *string `gorm:"-" json:"docPostCodeTh"`
	DocPostCodeEn                    *string `gorm:"-" json:"docPostCodeEn"`
	DocMasterSubDistrictCode         *string `gorm:"-" json:"docMasterSubDistrictCode"`
	DocMasterPostCodeSubDistrictCode *string `gorm:"-" json:"docMasterPostCodeSubDistrictCode"`
	DocMasterCityDescriptionTh       *string `gorm:"-" json:"docMasterCityDescriptionTh"`
	DocMasterCityDescriptionEn       *string `gorm:"-" json:"docMasterCityDescriptionEn"`

	// Registration Book Address (TYPE 3) - ignored during insert/update to main table
	BookAddressTypeID                 int     `gorm:"-" json:"bookAddressTypeId"`
	BookHouseNumber                   *string `gorm:"-" json:"bookHouseNumber"`
	BookMoo                           *string `gorm:"-" json:"bookMoo"`
	BookVillage                       *string `gorm:"-" json:"bookVillage"`
	BookRoomNumber                    *string `gorm:"-" json:"bookRoomNumber"`
	BookFloor                         *string `gorm:"-" json:"bookFloor"`
	BookBuilding                      *string `gorm:"-" json:"bookBuilding"`
	BookSoi                           *string `gorm:"-" json:"bookSoi"`
	BookRoad                          *string `gorm:"-" json:"bookRoad"`
	BookPostCodeID                    *int    `gorm:"-" json:"bookPostCodeId"`
	BookPostCodeTh                    *string `gorm:"-" json:"bookPostCodeTh"`
	BookPostCodeEn                    *string `gorm:"-" json:"bookPostCodeEn"`
	BookMasterSubDistrictCode         *string `gorm:"-" json:"bookMasterSubDistrictCode"`
	BookMasterPostCodeSubDistrictCode *string `gorm:"-" json:"bookMasterPostCodeSubDistrictCode"`
	BookMasterCityDescriptionTh       *string `gorm:"-" json:"bookMasterCityDescriptionTh"`
	BookMasterCityDescriptionEn       *string `gorm:"-" json:"bookMasterCityDescriptionEn"`

	// Shipping Address (TYPE 4) - ignored during insert/update to main table
	ShipAddressTypeID                 int     `gorm:"-" json:"shipAddressTypeId"`
	ShipHouseNumber                   *string `gorm:"-" json:"shipHouseNumber"`
	ShipMoo                           *string `gorm:"-" json:"shipMoo"`
	ShipVillage                       *string `gorm:"-" json:"shipVillage"`
	ShipRoomNumber                    *string `gorm:"-" json:"shipRoomNumber"`
	ShipFloor                         *string `gorm:"-" json:"shipFloor"`
	ShipBuilding                      *string `gorm:"-" json:"shipBuilding"`
	ShipSoi                           *string `gorm:"-" json:"shipSoi"`
	ShipRoad                          *string `gorm:"-" json:"shipRoad"`
	ShipPostCodeID                    *int    `gorm:"-" json:"shipPostCodeId"`
	ShipPostCodeTh                    *string `gorm:"-" json:"shipPostCodeTh"`
	ShipPostCodeEn                    *string `gorm:"-" json:"shipPostCodeEn"`
	ShipMasterSubDistrictCode         *string `gorm:"-" json:"shipMasterSubDistrictCode"`
	ShipMasterPostCodeSubDistrictCode *string `gorm:"-" json:"shipMasterPostCodeSubDistrictCode"`
	ShipMasterCityDescriptionTh       *string `gorm:"-" json:"shipMasterCityDescriptionTh"`
	ShipMasterCityDescriptionEn       *string `gorm:"-" json:"shipMasterCityDescriptionEn"`
}

func (BuyerRegistrationRequest) TableName() string {
	return "buyer_registration_request"
}
