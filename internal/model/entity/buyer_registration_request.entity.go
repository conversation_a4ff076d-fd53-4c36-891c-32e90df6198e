package entity

import (
	"backend-common-lib/model"
	"time"
)

type ApprovalStatusEnum string

const (
	ApprovalStatusPending  ApprovalStatusEnum = "WAITING"
	ApprovalStatusApproved ApprovalStatusEnum = "APPROVED"
	ApprovalStatusRejected ApprovalStatusEnum = "REJECTED"
)

type BuyerRegistrationRequest struct {
	*model.BaseEntity
	BuyerID        int                `gorm:"column:buyer_id;not null" json:"buyer_id"`
	IsActive       bool               `gorm:"column:is_active;not null" json:"is_active"`
	IsDeleted      bool               `gorm:"column:is_deleted;not null" json:"is_deleted"`
	RequestDate    *time.Time         `gorm:"column:request_date" json:"request_date"`
	ApprovalStatus ApprovalStatusEnum `gorm:"column:approval_status;type:approval_status_enum;default:PENDING;not null" json:"approval_status"`
	Remark         *string            `gorm:"column:remark" json:"remark"`

	// File information - ignored during insert/update to main table
	IdIdCardFile      *string `gorm:"column:id_id_card_file;<-:false" json:"idIdCardFile"`
	IdCardFile        *string `gorm:"column:id_card_file;<-:false" json:"idCardFile"`
	IdPermitDocFile   *string `gorm:"column:id_permit_doc_file;<-:false" json:"idPermitDocFile"`
	PermitDocFile     *string `gorm:"column:permit_doc_file;<-:false" json:"permitDocFile"`
	IdBankAccountFile *string `gorm:"column:id_bank_account_file;<-:false" json:"idBankAccountFile"`
	BankAccountFile   *string `gorm:"column:bank_account_file;<-:false" json:"bankAccountFile"`

	// Customer type information - ignored during insert/update to main table
	CustomerTypeTh   *string `gorm:"column:customer_type_th;<-:false" json:"customerTypeTh"`
	CustomerTypeEn   *string `gorm:"column:customer_type_en;<-:false" json:"customerTypeEn"`
	CustomerTypeCode *string `gorm:"column:customer_type_code;<-:false" json:"customerTypeCode"`

	// Buyer basic information - ignored during insert/update to main table
	PrefixNameID         int        `gorm:"column:prefix_name_id;<-:false" json:"prefixNameId"`
	PrefixTh             *string    `gorm:"column:prefix_th;<-:false" json:"prefixTh"`
	PrefixEn             *string    `gorm:"column:prefix_en;<-:false" json:"prefixEn"`
	FirstName            *string    `gorm:"column:first_name;<-:false" json:"firstName"`
	MiddleName           *string    `gorm:"column:middle_name;<-:false" json:"middleName"`
	LastName             *string    `gorm:"column:last_name;<-:false" json:"lastName"`
	DateOfBirth          *time.Time `gorm:"column:date_of_birth;<-:false" json:"dateOfBirth"`
	Nationality          *string    `gorm:"column:nationality;<-:false" json:"nationality"`
	IdentificationNumber *string    `gorm:"column:identification_number;<-:false" json:"identificationNumber"`

	// Registered Address (TYPE 1) - ignored during insert/update to main table
	RegAddressTypeID                 int     `gorm:"column:reg_address_type_id;<-:false" json:"regAddressTypeId"`
	RegHouseNumber                   *string `gorm:"column:reg_house_number;<-:false" json:"regHouseNumber"`
	RegMoo                           *string `gorm:"column:reg_moo;<-:false" json:"regMoo"`
	RegVillage                       *string `gorm:"column:reg_village;<-:false" json:"regVillage"`
	RegRoomNumber                    *string `gorm:"column:reg_room_number;<-:false" json:"regRoomNumber"`
	RegFloor                         *string `gorm:"column:reg_floor;<-:false" json:"regFloor"`
	RegBuilding                      *string `gorm:"column:reg_building;<-:false" json:"regBuilding"`
	RegSoi                           *string `gorm:"column:reg_soi;<-:false" json:"regSoi"`
	RegRoad                          *string `gorm:"column:reg_road;<-:false" json:"regRoad"`
	RegPostCodeID                    *int    `gorm:"column:reg_post_code_id;<-:false" json:"regPostCodeId"`
	RegPostCodeTh                    *string `gorm:"column:reg_post_code_th;<-:false" json:"regPostCodeTh"`
	RegPostCodeEn                    *string `gorm:"column:reg_post_code_en;<-:false" json:"regPostCodeEn"`
	RegMasterSubDistrictCode         *string `gorm:"column:reg_master_sub_district_code;<-:false" json:"regMasterSubDistrictCode"`
	RegMasterPostCodeSubDistrictCode *string `gorm:"column:reg_master_post_code_sub_district_code;<-:false" json:"regMasterPostCodeSubDistrictCode"`
	RegMasterCityDescriptionTh       *string `gorm:"column:reg_master_city_description_th;<-:false" json:"regMasterCityDescriptionTh"`
	RegMasterCityDescriptionEn       *string `gorm:"column:reg_master_city_description_en;<-:false" json:"regMasterCityDescriptionEn"`

	// Document Address (TYPE 2) - ignored during insert/update to main table
	DocAddressTypeID                 int     `gorm:"column:doc_address_type_id;<-:false" json:"docAddressTypeId"`
	DocHouseNumber                   *string `gorm:"column:doc_house_number;<-:false" json:"docHouseNumber"`
	DocMoo                           *string `gorm:"column:doc_moo;<-:false" json:"docMoo"`
	DocVillage                       *string `gorm:"column:doc_village;<-:false" json:"docVillage"`
	DocRoomNumber                    *string `gorm:"column:doc_room_number;<-:false" json:"docRoomNumber"`
	DocFloor                         *string `gorm:"column:doc_floor;<-:false" json:"docFloor"`
	DocBuilding                      *string `gorm:"column:doc_building;<-:false" json:"docBuilding"`
	DocSoi                           *string `gorm:"column:doc_soi;<-:false" json:"docSoi"`
	DocRoad                          *string `gorm:"column:doc_road;<-:false" json:"docRoad"`
	DocPostCodeID                    *int    `gorm:"column:doc_post_code_id;<-:false" json:"docPostCodeId"`
	DocPostCodeTh                    *string `gorm:"column:doc_post_code_th;<-:false" json:"docPostCodeTh"`
	DocPostCodeEn                    *string `gorm:"column:doc_post_code_en;<-:false" json:"docPostCodeEn"`
	DocMasterSubDistrictCode         *string `gorm:"column:doc_master_sub_district_code;<-:false" json:"docMasterSubDistrictCode"`
	DocMasterPostCodeSubDistrictCode *string `gorm:"column:doc_master_post_code_sub_district_code;<-:false" json:"docMasterPostCodeSubDistrictCode"`
	DocMasterCityDescriptionTh       *string `gorm:"column:doc_master_city_description_th;<-:false" json:"docMasterCityDescriptionTh"`
	DocMasterCityDescriptionEn       *string `gorm:"column:doc_master_city_description_en;<-:false" json:"docMasterCityDescriptionEn"`

	// Registration Book Address (TYPE 3) - ignored during insert/update to main table
	BookAddressTypeID                 int     `gorm:"column:book_address_type_id;<-:false" json:"bookAddressTypeId"`
	BookHouseNumber                   *string `gorm:"column:book_house_number;<-:false" json:"bookHouseNumber"`
	BookMoo                           *string `gorm:"column:book_moo;<-:false" json:"bookMoo"`
	BookVillage                       *string `gorm:"column:book_village;<-:false" json:"bookVillage"`
	BookRoomNumber                    *string `gorm:"column:book_room_number;<-:false" json:"bookRoomNumber"`
	BookFloor                         *string `gorm:"column:book_floor;<-:false" json:"bookFloor"`
	BookBuilding                      *string `gorm:"column:book_building;<-:false" json:"bookBuilding"`
	BookSoi                           *string `gorm:"column:book_soi;<-:false" json:"bookSoi"`
	BookRoad                          *string `gorm:"column:book_road;<-:false" json:"bookRoad"`
	BookPostCodeID                    *int    `gorm:"column:book_post_code_id;<-:false" json:"bookPostCodeId"`
	BookPostCodeTh                    *string `gorm:"column:book_post_code_th;<-:false" json:"bookPostCodeTh"`
	BookPostCodeEn                    *string `gorm:"column:book_post_code_en;<-:false" json:"bookPostCodeEn"`
	BookMasterSubDistrictCode         *string `gorm:"column:book_master_sub_district_code;<-:false" json:"bookMasterSubDistrictCode"`
	BookMasterPostCodeSubDistrictCode *string `gorm:"column:book_master_post_code_sub_district_code;<-:false" json:"bookMasterPostCodeSubDistrictCode"`
	BookMasterCityDescriptionTh       *string `gorm:"column:book_master_city_description_th;<-:false" json:"bookMasterCityDescriptionTh"`
	BookMasterCityDescriptionEn       *string `gorm:"column:book_master_city_description_en;<-:false" json:"bookMasterCityDescriptionEn"`

	// Shipping Address (TYPE 4) - ignored during insert/update to main table
	ShipAddressTypeID                 int     `gorm:"column:ship_address_type_id;<-:false" json:"shipAddressTypeId"`
	ShipHouseNumber                   *string `gorm:"column:ship_house_number;<-:false" json:"shipHouseNumber"`
	ShipMoo                           *string `gorm:"column:ship_moo;<-:false" json:"shipMoo"`
	ShipVillage                       *string `gorm:"column:ship_village;<-:false" json:"shipVillage"`
	ShipRoomNumber                    *string `gorm:"column:ship_room_number;<-:false" json:"shipRoomNumber"`
	ShipFloor                         *string `gorm:"column:ship_floor;<-:false" json:"shipFloor"`
	ShipBuilding                      *string `gorm:"column:ship_building;<-:false" json:"shipBuilding"`
	ShipSoi                           *string `gorm:"column:ship_soi;<-:false" json:"shipSoi"`
	ShipRoad                          *string `gorm:"column:ship_road;<-:false" json:"shipRoad"`
	ShipPostCodeID                    *int    `gorm:"column:ship_post_code_id;<-:false" json:"shipPostCodeId"`
	ShipPostCodeTh                    *string `gorm:"column:ship_post_code_th;<-:false" json:"shipPostCodeTh"`
	ShipPostCodeEn                    *string `gorm:"column:ship_post_code_en;<-:false" json:"shipPostCodeEn"`
	ShipMasterSubDistrictCode         *string `gorm:"column:ship_master_sub_district_code;<-:false" json:"shipMasterSubDistrictCode"`
	ShipMasterPostCodeSubDistrictCode *string `gorm:"column:ship_master_post_code_sub_district_code;<-:false" json:"shipMasterPostCodeSubDistrictCode"`
	ShipMasterCityDescriptionTh       *string `gorm:"column:ship_master_city_description_th;<-:false" json:"shipMasterCityDescriptionTh"`
	ShipMasterCityDescriptionEn       *string `gorm:"column:ship_master_city_description_en;<-:false" json:"shipMasterCityDescriptionEn"`
}

func (BuyerRegistrationRequest) TableName() string {
	return "buyer_registration_request"
}
