package service

import (
	"fmt"
	"net/http"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"strings"

	"github.com/gofiber/fiber/v2"
)

func (s *buyerRegistrationRequest) SearchBuyerRegistrationRequestFilter(req dto.BuyerRegistrationRequestPageReqDto) (dto.BuyerRegistrationRequestPageRespDto[dto.BuyerRegistrationRequestDto], error) {
	resp := dto.BuyerRegistrationRequestPageRespDto[dto.BuyerRegistrationRequestDto]{}
	result, err := s.Repo.FindBuyerRegistrationRequestWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.BuyerRegistrationRequestDto, len(result))
	for i, v := range result {
		// Convert BuyerRegistrationRequestDetailDto to BuyerRegistrationRequestDto
		var reqAddressTypeId *int
		var docAddressTypeId *int
		var bookAddressTypeId *int
		var shipAddressTypeId *int

		regAddr := buildRegisteredAddress(v)
		reqAddressTypeId = &v.RegAddressTypeID
		docAddr := buildDocumentAddress(v)
		docAddressTypeId = &v.DocAddressTypeID
		// if docAddr == nil {
		// 	docAddr = regAddr
		// 	docAddressTypeId = reqAddressTypeId
		// }
		bookAddr := buildRegistrationBookAddress(v)
		bookAddressTypeId = &v.BookAddressTypeID
		// if bookAddr == nil {
		// 	bookAddr = regAddr
		// 	bookAddressTypeId = reqAddressTypeId
		// }
		shipAddr := buildShippingAddress(v)
		shipAddressTypeId = &v.ShipAddressTypeID
		// if shipAddr == nil {
		// 	shipAddr = regAddr
		// 	shipAddressTypeId = reqAddressTypeId
		// }

		mapResult[i] = dto.BuyerRegistrationRequestDto{
			BaseDto: model.BaseDto{
				Id: v.ID,
			},
			RequestDate:             v.RequestDate,
			PersonTypeId:            v.CustomerTypeCode,
			PersonTypeTh:            v.CustomerTypeTh,
			PersonTypeEn:            v.CustomerTypeEn,
			NationalId:              v.IdentificationNumber,
			TitleTh:                 v.PrefixTh,
			TitleEn:                 v.PrefixEn,
			FirstNameTh:             v.FirstName,
			FirstNameEn:             v.FirstName,
			LastNameTh:              v.LastName,
			LastNameEn:              v.LastName,
			DateOfBirth:             v.DateOfBirth,
			RegisteredAddressTypeId: reqAddressTypeId,
			RegisteredAddress:       regAddr,
			DocumentAddressTypeId:   docAddressTypeId,
			DocumentAddress:         docAddr,
			RegistrationBookTypeId:  bookAddressTypeId,
			RegistrationBookAddress: bookAddr,
			ShippingAddressTypeId:   shipAddressTypeId,
			ShippingAddress:         shipAddr,
			ApprovalStatus:          (*string)(&v.ApprovalStatus),
			RejectReason:            v.Remark,
		}

	}

	//NOTE - Response
	resp = dto.BuyerRegistrationRequestPageRespDto[dto.BuyerRegistrationRequestDto]{
		PagingModel: *util.MapPaginationResult(mapResult, len(mapResult), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
	}

	return resp, nil
}

func (s *buyerRegistrationRequest) UpdateBuyerRegistrationRequestStatus(req dto.BuyerRegistrationRequestUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"approval_status": req.ApprovalStatus,
		"updated_by":      req.ActionBy,
		"updated_date":    util.Now(),
	}

	affectedRows, err := s.Repo.UpdateStatus(req.Id, fieldsToUpdate)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

// Helper function to build registered address string
func buildRegisteredAddress(v dto.BuyerRegistrationRequestDetailDto) *string {

	// บ้านเลขที่ + ห้องเลขที่ + ชั้น + อาคาร + หมูบ้าน + หมู่ที่ + ซอย + ถนน + แขวง + เขต + จังหวัด + รหัสไปรษณีย์ + ประเทศ​

	if v.RegHouseNumber == nil && v.RegRoomNumber == nil && v.RegFloor == nil && v.RegBuilding == nil && v.RegVillage == nil && v.RegMoo == nil && v.RegSoi == nil && v.RegRoad == nil && v.RegMasterCityDescriptionTh == nil && v.RegPostCodeID == nil {
		return nil
	}

	var parts []string
	if v.RegHouseNumber != nil && *v.RegHouseNumber != "" {
		parts = append(parts, *v.RegHouseNumber)
	}
	if v.RegRoomNumber != nil && *v.RegRoomNumber != "" {
		parts = append(parts, "ห้อง "+*v.RegRoomNumber)
	}
	if v.RegFloor != nil && *v.RegFloor != "" {
		parts = append(parts, "ชั้น "+*v.RegFloor)
	}
	if v.RegBuilding != nil && *v.RegBuilding != "" {
		parts = append(parts, "อาคาร "+*v.RegBuilding)
	}
	if v.RegMoo != nil && *v.RegMoo != "" {
		parts = append(parts, "หมู่ "+*v.RegMoo)
	}
	if v.RegVillage != nil && *v.RegVillage != "" {
		parts = append(parts, *v.RegVillage)
	}
	if v.RegSoi != nil && *v.RegSoi != "" {
		parts = append(parts, "ซอย "+*v.RegSoi)
	}
	if v.RegRoad != nil && *v.RegRoad != "" {
		parts = append(parts, "ถนน "+*v.RegRoad)
	}
	if v.RegMasterCityDescriptionTh != nil && *v.RegMasterCityDescriptionTh != "" {
		parts = append(parts, *v.RegMasterCityDescriptionTh)
	}
	if v.RegPostCodeID != nil {
		parts = append(parts, fmt.Sprintf("%d", *v.RegPostCodeID))
	}

	if len(parts) == 0 {
		return nil
	}

	address := strings.Join(parts, " ")
	return &address
}

// Helper function to build document address string
func buildDocumentAddress(v dto.BuyerRegistrationRequestDetailDto) *string {
	if v.DocHouseNumber == nil && v.DocRoomNumber == nil && v.DocFloor == nil && v.DocBuilding == nil && v.DocVillage == nil && v.DocMoo == nil && v.DocSoi == nil && v.DocRoad == nil && v.DocMasterCityDescriptionTh == nil && v.DocPostCodeID == nil {
		return nil
	}

	var parts []string
	if v.DocHouseNumber != nil && *v.DocHouseNumber != "" {
		parts = append(parts, *v.DocHouseNumber)
	}
	if v.DocRoomNumber != nil && *v.DocRoomNumber != "" {
		parts = append(parts, "ห้อง "+*v.DocRoomNumber)
	}
	if v.DocFloor != nil && *v.DocFloor != "" {
		parts = append(parts, "ชั้น "+*v.DocFloor)
	}
	if v.DocBuilding != nil && *v.DocBuilding != "" {
		parts = append(parts, "อาคาร "+*v.DocBuilding)
	}
	if v.DocVillage != nil && *v.DocVillage != "" {
		parts = append(parts, *v.DocVillage)
	}
	if v.DocMoo != nil && *v.DocMoo != "" {
		parts = append(parts, "หมู่ "+*v.DocMoo)
	}
	if v.DocSoi != nil && *v.DocSoi != "" {
		parts = append(parts, "ซอย "+*v.DocSoi)
	}
	if v.DocRoad != nil && *v.DocRoad != "" {
		parts = append(parts, "ถนน "+*v.DocRoad)
	}
	if v.DocMasterCityDescriptionTh != nil && *v.DocMasterCityDescriptionTh != "" {
		parts = append(parts, *v.DocMasterCityDescriptionTh)
	}
	if v.DocPostCodeID != nil {
		parts = append(parts, fmt.Sprintf("%d", *v.DocPostCodeID))
	}

	if len(parts) == 0 {
		return nil
	}

	address := strings.Join(parts, " ")
	return &address
}

// Helper function to build registration book address string
func buildRegistrationBookAddress(v dto.BuyerRegistrationRequestDetailDto) *string {
	if v.BookHouseNumber == nil && v.BookRoomNumber == nil && v.BookFloor == nil && v.BookBuilding == nil && v.BookVillage == nil && v.BookMoo == nil && v.BookSoi == nil && v.BookRoad == nil && v.BookMasterCityDescriptionTh == nil && v.BookPostCodeID == nil {
		return nil
	}

	var parts []string
	if v.BookHouseNumber != nil && *v.BookHouseNumber != "" {
		parts = append(parts, *v.BookHouseNumber)
	}
	if v.BookRoomNumber != nil && *v.BookRoomNumber != "" {
		parts = append(parts, "ห้อง "+*v.BookRoomNumber)
	}
	if v.BookFloor != nil && *v.BookFloor != "" {
		parts = append(parts, "ชั้น "+*v.BookFloor)
	}
	if v.BookBuilding != nil && *v.BookBuilding != "" {
		parts = append(parts, "อาคาร "+*v.BookBuilding)
	}
	if v.BookVillage != nil && *v.BookVillage != "" {
		parts = append(parts, *v.BookVillage)
	}
	if v.BookMoo != nil && *v.BookMoo != "" {
		parts = append(parts, "หมู่ "+*v.BookMoo)
	}
	if v.BookSoi != nil && *v.BookSoi != "" {
		parts = append(parts, "ซอย "+*v.BookSoi)
	}
	if v.BookRoad != nil && *v.BookRoad != "" {
		parts = append(parts, "ถนน "+*v.BookRoad)
	}
	if v.BookMasterCityDescriptionTh != nil && *v.BookMasterCityDescriptionTh != "" {
		parts = append(parts, *v.BookMasterCityDescriptionTh)
	}
	if v.BookPostCodeID != nil {
		parts = append(parts, fmt.Sprintf("%d", *v.BookPostCodeID))
	}

	if len(parts) == 0 {
		return nil
	}

	address := strings.Join(parts, " ")
	return &address
}

func buildShippingAddress(v dto.BuyerRegistrationRequestDetailDto) *string {
	if v.ShipHouseNumber == nil && v.ShipRoomNumber == nil && v.ShipFloor == nil && v.ShipBuilding == nil && v.ShipVillage == nil && v.ShipMoo == nil && v.ShipSoi == nil && v.ShipRoad == nil && v.ShipMasterCityDescriptionTh == nil && v.ShipPostCodeID == nil {
		return nil
	}

	var parts []string
	if v.ShipHouseNumber != nil && *v.ShipHouseNumber != "" {
		parts = append(parts, *v.ShipHouseNumber)
	}
	if v.ShipRoomNumber != nil && *v.ShipRoomNumber != "" {
		parts = append(parts, "ห้อง "+*v.ShipRoomNumber)
	}
	if v.ShipFloor != nil && *v.ShipFloor != "" {
		parts = append(parts, "ชั้น "+*v.ShipFloor)
	}
	if v.ShipBuilding != nil && *v.ShipBuilding != "" {
		parts = append(parts, "อาคาร "+*v.ShipBuilding)
	}
	if v.ShipVillage != nil && *v.ShipVillage != "" {
		parts = append(parts, *v.ShipVillage)
	}
	if v.ShipMoo != nil && *v.ShipMoo != "" {
		parts = append(parts, "หมู่ "+*v.ShipMoo)
	}
	if v.ShipSoi != nil && *v.ShipSoi != "" {
		parts = append(parts, "ซอย "+*v.ShipSoi)
	}
	if v.ShipRoad != nil && *v.ShipRoad != "" {
		parts = append(parts, "ถนน "+*v.ShipRoad)
	}
	if v.ShipMasterCityDescriptionTh != nil && *v.ShipMasterCityDescriptionTh != "" {
		parts = append(parts, *v.ShipMasterCityDescriptionTh)
	}
	if v.ShipPostCodeID != nil {
		parts = append(parts, fmt.Sprintf("%d", *v.ShipPostCodeID))
	}

	if len(parts) == 0 {
		return nil
	}

	address := strings.Join(parts, " ")
	return &address
}
