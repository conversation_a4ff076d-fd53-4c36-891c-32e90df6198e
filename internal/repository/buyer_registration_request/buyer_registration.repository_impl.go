package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

func (r *buyerRegistrationRequestRepositoryImpl) GetById(id int) (entity.Buyer, error) {
	var buyer entity.Buyer
	err := r.DB.First(&buyer, id).Error
	return buyer, err
}

// Helper function to build query arguments
func getQueryArgs(req dto.BuyerRegistrationRequestPageReqDto) []interface{} {
	var args []interface{}

	// Add search arguments (repeat to exactly match the number of `?` placeholders in the name/prefix search block)
	if req.Search != nil && *req.Search != "" {
		searchTerm := "%" + *req.Search + "%"
		// There are 7 placeholders: first_name, middle_name, last_name, first+last, first+middle+last, prefix_th, prefix_en
		const searchPlaceholderCount = 7
		for i := 0; i < searchPlaceholderCount; i++ {
			args = append(args, searchTerm)
		}
	}

	// Add approval status filter
	if req.ApprovalStatus != "" {
		args = append(args, req.ApprovalStatus)
	}

	// Add account type filter
	if req.AccountType != nil && *req.AccountType != "" {
		args = append(args, *req.AccountType)
	}

	// Add nationality filter
	if req.Nationality != nil && *req.Nationality != "" {
		args = append(args, *req.Nationality)
	}

	return args
}

func (r *buyerRegistrationRequestRepositoryImpl) FindBuyerRegistrationRequestWithFilter(req dto.BuyerRegistrationRequestPageReqDto) ([]entity.BuyerRegistrationRequest, error) {
	var results []entity.BuyerRegistrationRequest

	// Build the complex SQL query with all the joins
	query := `
		SELECT
			brr.id,
			brr.buyer_id,
			brr.request_date,
			bf_id_card.id AS id_id_card_file,
			bf_id_card.filename AS id_card_file,
			bf_permit.id  AS id_permit_doc_file,
			bf_permit.filename  AS permit_doc_file,
			bf_bank.id    AS id_bank_account_file,
			bf_bank.filename    AS bank_account_file,
			brr.approval_status,

			mct.description_th AS customer_type_th,
			mct.description_en AS customer_type_en,
			mct.customer_type_code,
			b.prefix_name_id,
			mpn.description_th AS prefix_th,
			mpn.description_en AS prefix_en,
			b.first_name,
			b.middle_name,
			b.last_name,
			b.date_of_birth,
			b.nationality,
			b.identification_number,
			brr.remark,

			-- REGISTERED ADDRESS (TYPE 1)
			ba_reg.address_type AS reg_address_type_id,
			ba_reg.house_number AS reg_house_number,
			ba_reg.moo AS reg_moo,
			ba_reg.village AS reg_village,
			ba_reg.room_number AS reg_room_number,
			ba_reg.floor AS reg_floor,
			ba_reg.building AS reg_building,
			ba_reg.soi AS reg_soi,
			ba_reg.road AS reg_road,
			CAST(ba_reg.post_code AS INT) AS reg_post_code_id,
			mpc_reg.description_th AS reg_post_code_th,
			mpc_reg.description_en AS reg_post_code_en,
			msd_reg.sub_district_code AS reg_master_sub_district_code,
			mpc_reg.sub_district_code AS reg_master_post_code_sub_district_code,
			mc_reg.description_th AS reg_master_city_description_th,
			mc_reg.description_en AS reg_master_city_description_en,

			-- DOCUMENT ADDRESS (TYPE 2)
			ba_doc.address_type AS doc_address_type_id,
			ba_doc.house_number AS doc_house_number,
			ba_doc.moo AS doc_moo,
			ba_doc.village AS doc_village,
			ba_doc.room_number AS doc_room_number,
			ba_doc.floor AS doc_floor,
			ba_doc.building AS doc_building,
			ba_doc.soi AS doc_soi,
			ba_doc.road AS doc_road,
			CAST(ba_doc.post_code AS INT) AS doc_post_code_id,
			mpc_doc.description_th AS doc_post_code_th,
			mpc_doc.description_en AS doc_post_code_en,
			msd_doc.sub_district_code AS doc_master_sub_district_code,
			mpc_doc.sub_district_code AS doc_master_post_code_sub_district_code,
			mc_doc.description_th AS doc_master_city_description_th,
			mc_doc.description_en AS doc_master_city_description_en,

			-- REGISTRATION BOOK ADDRESS (TYPE 3)
			ba_book.address_type AS book_address_type_id,
			ba_book.house_number AS book_house_number,
			ba_book.moo AS book_moo,
			ba_book.village AS book_village,
			ba_book.room_number AS book_room_number,
			ba_book.floor AS book_floor,
			ba_book.building AS book_building,
			ba_book.soi AS book_soi,
			ba_book.road AS book_road,
			CAST(ba_book.post_code AS INT) AS book_post_code_id,
			mpc_book.description_th AS book_post_code_th,
			mpc_book.description_en AS book_post_code_en,
			msd_book.sub_district_code AS book_master_sub_district_code,
			mpc_book.sub_district_code AS book_master_post_code_sub_district_code,
			mc_book.description_th AS book_master_city_description_th,
			mc_book.description_en AS book_master_city_description_en,

			-- SHIPPING ADDRESS (TYPE 4)
			ba_ship.address_type AS ship_address_type_id,
			ba_ship.house_number AS ship_house_number,
			ba_ship.moo AS ship_moo,
			ba_ship.village AS ship_village,
			ba_ship.room_number AS ship_room_number,
			ba_ship.floor AS ship_floor,
			ba_ship.building AS ship_building,
			ba_ship.soi AS ship_soi,
			ba_ship.road AS ship_road,
			CAST(ba_ship.post_code AS INT) AS ship_post_code_id,
			mpc_ship.description_th AS ship_post_code_th,
			mpc_ship.description_en AS ship_post_code_en,
			msd_ship.sub_district_code AS ship_master_sub_district_code,
			mpc_ship.sub_district_code AS ship_master_post_code_sub_district_code,
			mc_ship.description_th AS ship_master_city_description_th,
			mc_ship.description_en AS ship_master_city_description_en

		FROM buyer_registration_request brr
		LEFT JOIN buyer_files bf_id_card ON bf_id_card.buyer_id = brr.buyer_id AND bf_id_card.file_category = 'ID_CARD'
		LEFT JOIN buyer_files bf_permit   ON bf_permit.buyer_id   = brr.buyer_id AND bf_permit.file_category   = 'PERMIT_DOC'
		LEFT JOIN buyer_files bf_bank     ON bf_bank.buyer_id     = brr.buyer_id AND bf_bank.file_category     = 'BANK_ACCOUNT'
		LEFT JOIN buyer b ON b.id = brr.buyer_id
		LEFT JOIN master_prefix_name mpn ON b.prefix_name_id = mpn.id
		LEFT JOIN master_customer_type mct ON b.account_type = mct.id

		-- REGISTERED ADDRESS (TYPE 1)
		LEFT JOIN buyer_address ba_reg ON ba_reg.buyer_id = brr.buyer_id AND ba_reg.address_type = 1
		LEFT JOIN master_district md_reg ON ba_reg.district_id = md_reg.id
		LEFT JOIN master_sub_district msd_reg ON ba_reg.sub_district_id = msd_reg.id
		LEFT JOIN master_post_code mpc_reg ON ba_reg.post_code = mpc_reg.post_code AND msd_reg.sub_district_code = mpc_reg.sub_district_code
		LEFT JOIN master_city mc_reg ON ba_reg.province_id = mc_reg.id

		-- DOCUMENT ADDRESS (TYPE 2; fallback -> TYPE 1)
		LEFT JOIN LATERAL (
			SELECT *
			FROM buyer_address ba
			WHERE ba.buyer_id = brr.buyer_id
			  AND ba.address_type IN (2,1)
			ORDER BY CASE WHEN ba.address_type = 2 THEN 1 ELSE 2 END
			LIMIT 1
		) ba_doc ON TRUE
		LEFT JOIN master_district md_doc ON ba_doc.district_id = md_doc.id
		LEFT JOIN master_sub_district msd_doc ON ba_doc.sub_district_id = msd_doc.id
		LEFT JOIN master_post_code mpc_doc ON ba_doc.post_code = mpc_doc.post_code AND msd_doc.sub_district_code = mpc_doc.sub_district_code
		LEFT JOIN master_city mc_doc ON ba_doc.province_id = mc_doc.id

		-- REGISTRATION BOOK ADDRESS (TYPE 3; fallback -> TYPE 1)
		LEFT JOIN LATERAL (
			SELECT *
			FROM buyer_address ba
			WHERE ba.buyer_id = brr.buyer_id
			  AND ba.address_type IN (3,1)
			ORDER BY CASE WHEN ba.address_type = 3 THEN 1 ELSE 2 END
			LIMIT 1
		) ba_book ON TRUE
		LEFT JOIN master_district md_book ON ba_book.district_id = md_book.id
		LEFT JOIN master_sub_district msd_book ON ba_book.sub_district_id = msd_book.id
		LEFT JOIN master_post_code mpc_book ON ba_book.post_code = mpc_book.post_code AND msd_book.sub_district_code = mpc_book.sub_district_code
		LEFT JOIN master_city mc_book ON ba_book.province_id = mc_book.id

		-- SHIPPING ADDRESS (TYPE 4; fallback -> TYPE 1)
		LEFT JOIN LATERAL (
			SELECT *
			FROM buyer_address ba
			WHERE ba.buyer_id = brr.buyer_id
			  AND ba.address_type IN (4,1)
			ORDER BY CASE WHEN ba.address_type = 4 THEN 1 ELSE 2 END
			LIMIT 1
		) ba_ship ON TRUE
		LEFT JOIN master_district md_ship ON ba_ship.district_id = md_ship.id
		LEFT JOIN master_sub_district msd_ship ON ba_ship.sub_district_id = msd_ship.id
		LEFT JOIN master_post_code mpc_ship ON ba_ship.post_code = mpc_ship.post_code AND msd_ship.sub_district_code = mpc_ship.sub_district_code
		LEFT JOIN master_city mc_ship ON ba_ship.province_id = mc_ship.id

		WHERE 1=1`

	// Add search conditions
	// สามารถค้นหา คำนำหน้า (title th/en), ชื่อ (first_name th/en), ชื่อกลาง (middle_name th/en), นามสกุล (last_name th/en)
	if req.Search != nil && *req.Search != "" {
		query += `
			AND (
				-- Buyer name-only search
				(b.first_name ILIKE ?)
				OR (b.middle_name ILIKE ?)
				OR (b.last_name ILIKE ?)
				OR (COALESCE(b.first_name,'') || ' ' || COALESCE(b.last_name,'')) ILIKE ?
				OR (COALESCE(b.first_name,'') || ' ' || COALESCE(b.middle_name,'') || ' ' || COALESCE(b.last_name,'')) ILIKE ?

				-- Prefix (title th/en)
				OR mpn.description_th ILIKE ?
				OR mpn.description_en ILIKE ?
			)`
	}

	// Add approval status filter
	if req.ApprovalStatus != "" {
		query += ` AND brr.approval_status = ?`
	}

	// Add account type filter
	if req.AccountType != nil && *req.AccountType != "" {
		query += ` AND mct.customer_type_code = ?`
	}

	// Add nationality filter
	if req.Nationality != nil && *req.Nationality != "" {
		query += ` AND b.nationality = ?`
	}

	// Add sorting
	if req.SortBy != "" {
		req.SortBy = util.CamelToSnake(req.SortBy)
		// Map of sort keys to DB columns (as slices)

		sortKeyToCols := map[string][]string{
			"id":              {"brr.id"},
			"request_date":    {"brr.request_date"},
			"person_type_th":  {"mct.description_th"},
			"person_type_en":  {"mct.description_en"},
			"national_id":     {"b.identification_number"},
			"approval_status": {"brr.approval_status"},
			"title_th":        {"mpn.description_th"},
			"title_en":        {"mpn.description_en"},
			"middle_name_th":  {"b.middle_name"},
			"middle_name_en":  {"b.middle_name"},
			"date_of_birth":   {"b.date_of_birth"},
			"first_name_th":   {"b.first_name"},
			"first_name_en":   {"b.first_name"},
			"last_name_th":    {"b.last_name"},
			"last_name_en":    {"b.last_name"},
			"registered_address": {
				"ba_reg.house_number", "ba_reg.moo", "ba_reg.village", "ba_reg.room_number", "ba_reg.floor", "ba_reg.building", "ba_reg.soi", "ba_reg.road", "ba_reg.post_code", "mpc_reg.description_th", "mpc_reg.description_en", "msd_reg.sub_district_code", "mpc_reg.sub_district_code", "mc_reg.description_th", "mc_reg.description_en",
			},
			"document_address": {
				"ba_doc.house_number", "ba_doc.moo", "ba_doc.village", "ba_doc.room_number", "ba_doc.floor", "ba_doc.building", "ba_doc.soi", "ba_doc.road", "ba_doc.post_code", "mpc_doc.description_th", "mpc_doc.description_en", "msd_doc.sub_district_code", "mpc_doc.sub_district_code", "mc_doc.description_th", "mc_doc.description_en",
			},
			"registration_book_address": {
				"ba_book.house_number", "ba_book.moo", "ba_book.village", "ba_book.room_number", "ba_book.floor", "ba_book.building", "ba_book.soi", "ba_book.road", "ba_book.post_code", "mpc_book.description_th", "mpc_book.description_en", "msd_book.sub_district_code", "mpc_book.sub_district_code", "mc_book.description_th", "mc_book.description_en",
			},
			"shipping_address": {
				"ba_ship.house_number", "ba_ship.moo", "ba_ship.village", "ba_ship.room_number", "ba_ship.floor", "ba_ship.building", "ba_ship.soi", "ba_ship.road", "ba_ship.post_code", "mpc_ship.description_th", "mpc_ship.description_en", "msd_ship.sub_district_code", "mpc_ship.sub_district_code", "mc_ship.description_th", "mc_ship.description_en",
			},
			"reject_reason": {"brr.remark"},
		}

		sortFields := strings.Split(req.SortBy, ",")
		sortOrders := []string{}
		if strings.Contains(req.SortOrder, ",") {
			sortOrders = strings.Split(req.SortOrder, ",")
		} else if req.SortOrder != "" {
			for range sortFields {
				sortOrders = append(sortOrders, req.SortOrder)
			}
		} else {
			for range sortFields {
				sortOrders = append(sortOrders, "ASC")
			}
		}

		var orderClauses []string
		for i, field := range sortFields {
			field = strings.TrimSpace(field)
			if field == "" {
				continue
			}
			order := "ASC"
			if i < len(sortOrders) && strings.EqualFold(strings.TrimSpace(sortOrders[i]), "desc") {
				order = "DESC"
			}
			if cols, ok := sortKeyToCols[field]; ok {
				for _, col := range cols {
					orderClauses = append(orderClauses, fmt.Sprintf("%s %s", col, order))
				}
			}
		}
		// Always fallback to brr.id ASC for stable ordering
		orderClauses = append(orderClauses, "brr.id ASC")
		query += "\nORDER BY " + strings.Join(orderClauses, ", ")
	} else {
		query += ` ORDER BY brr.id ASC`
	}

	// Add pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query += fmt.Sprintf(` LIMIT %d OFFSET %d`, req.PageLimit, offset)

	// Execute the query
	if err := r.DB.Raw(query, getQueryArgs(req)...).Scan(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *buyerRegistrationRequestRepositoryImpl) FindById(id int) (*entity.BuyerRegistrationRequest, error) {
	var result entity.BuyerRegistrationRequest
	err := r.DB.First(&result, id).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (r *buyerRegistrationRequestRepositoryImpl) UpdateStatus(id int, fields map[string]interface{}) (int64, error) {
	result := r.DB.Model(&entity.BuyerRegistrationRequest{}).Where("id = ?", id).Updates(fields)
	return result.RowsAffected, result.Error
}

func (r *buyerRegistrationRequestRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
