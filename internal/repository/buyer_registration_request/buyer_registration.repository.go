package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type buyerRegistrationRequestRepositoryImpl struct {
	DB *gorm.DB
}

type BuyerRegistrationRequestRepository interface {
	FindBuyerRegistrationRequestWithFilter(req dto.BuyerRegistrationRequestPageReqDto) ([]entity.BuyerRegistrationRequest, error)
	FindById(id int) (*entity.BuyerRegistrationRequest, error)
	UpdateStatus(id int, fields map[string]interface{}) (int64, error)
	GetDB() *gorm.DB
}

func NewBuyerRegistrationRequestRepository(db *gorm.DB) BuyerRegistrationRequestRepository {
	return &buyerRegistrationRequestRepositoryImpl{DB: db}
}
